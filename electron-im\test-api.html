<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - 联系人列表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .contact-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .contact-name {
            font-weight: bold;
            font-size: 16px;
        }
        .contact-status {
            color: #666;
            font-size: 14px;
        }
        .last-message {
            color: #888;
            font-size: 12px;
            margin-top: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .timestamp {
            color: #999;
            font-size: 11px;
        }
        .has-message {
            background-color: #f0f8ff;
        }
        .no-message {
            background-color: #f9f9f9;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>联系人列表API测试</h1>
    <p>测试新的API响应格式，包含lastMessage字段和排序逻辑</p>
    
    <button onclick="testApiResponse()">测试API响应</button>
    <button onclick="testSorting()">测试排序逻辑</button>
    
    <div id="results"></div>

    <script>
        // 模拟API响应数据
        const mockApiResponse = {
            success: true,
            users: [
                {
                    id: "689da531165addb9bbe1ea1c",
                    username: "chenhaowen",
                    email: "<EMAIL>",
                    displayName: "陈颢文",
                    avatar: "/avatars/default.png",
                    department: "技术部",
                    position: "前端工程师",
                    isOnline: true,
                    lastOnlineTime: "2025-08-18T11:47:26.813Z",
                    lastMessage: null
                },
                {
                    id: "689da531165addb9bbe1ea86",
                    username: "chenjiayao",
                    email: "<EMAIL>",
                    displayName: "陈嘉瑶",
                    avatar: "/avatars/default.png",
                    department: "技术部",
                    position: "前端工程师",
                    isOnline: true,
                    lastOnlineTime: "2025-08-18T11:05:37.728Z",
                    lastMessage: {
                        senderName: "我",
                        timestamp: "2025-08-18T11:01:09.012Z",
                        content: "吃饭"
                    }
                },
                {
                    id: "689da531165addb9bbe1ea45",
                    username: "chenjun",
                    email: "<EMAIL>",
                    displayName: "陈俊",
                    avatar: "/avatars/default.png",
                    department: "技术部",
                    position: "前端工程师",
                    isOnline: true,
                    lastOnlineTime: "2025-08-18T11:47:09.874Z",
                    lastMessage: null
                },
                {
                    id: "689da531165addb9bbe1ea24",
                    username: "chenqian",
                    email: "<EMAIL>",
                    displayName: "陈前",
                    avatar: "/avatars/default.png",
                    department: "技术部",
                    position: "前端工程师",
                    isOnline: false,
                    lastOnlineTime: "2025-08-16T08:47:49.311Z",
                    lastMessage: null
                }
            ]
        };

        function processUsers(users) {
            // 模拟Chat.vue中的处理逻辑
            const filteredUsers = users.map(user => {
                return {
                    id: user.id,
                    name: user.displayName,
                    status: user.isOnline ? '在线' : '离线',
                    lastMessage: user.lastMessage?.content || '', // 有lastMessage显示内容，没有则为空字符串
                    lastMessageTime: user.lastMessage 
                        ? new Date(user.lastMessage.timestamp)
                        : new Date(user.lastOnlineTime), // 有lastMessage用消息时间，没有用最后在线时间
                    user: user,
                    hasLastMessage: !!user.lastMessage // 标记是否有最后消息，用于排序
                };
            });

            // 按照最后消息时间排序：有消息的在前，按时间倒序；没有消息的在后，按最后在线时间倒序
            return filteredUsers.sort((a, b) => {
                // 如果都有最后消息，按消息时间倒序
                if (a.hasLastMessage && b.hasLastMessage) {
                    return b.lastMessageTime.getTime() - a.lastMessageTime.getTime();
                }
                // 如果都没有最后消息，按最后在线时间倒序
                if (!a.hasLastMessage && !b.hasLastMessage) {
                    return b.lastMessageTime.getTime() - a.lastMessageTime.getTime();
                }
                // 有消息的排在没消息的前面
                if (a.hasLastMessage && !b.hasLastMessage) {
                    return -1;
                }
                if (!a.hasLastMessage && b.hasLastMessage) {
                    return 1;
                }
                return 0;
            });
        }

        function formatTime(date) {
            const now = new Date();
            const diff = now.getTime() - date.getTime();

            if (diff < 1000 * 60) {
                return '刚刚';
            } else if (diff < 1000 * 60 * 60) {
                return `${Math.floor(diff / (1000 * 60))}分钟前`;
            } else if (diff < 1000 * 60 * 60 * 24) {
                return `${Math.floor(diff / (1000 * 60 * 60))}小时前`;
            } else {
                return date.toLocaleDateString();
            }
        }

        function testApiResponse() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>API响应测试结果</h2>';
            
            console.log('原始API响应:', mockApiResponse);
            
            const processedUsers = processUsers(mockApiResponse.users);
            console.log('处理后的用户列表:', processedUsers);
            
            processedUsers.forEach(contact => {
                const contactDiv = document.createElement('div');
                contactDiv.className = `contact-item ${contact.hasLastMessage ? 'has-message' : 'no-message'}`;
                
                contactDiv.innerHTML = `
                    <div class="contact-name">${contact.name}</div>
                    <div class="contact-status">${contact.status} | ${contact.user.department} - ${contact.user.position}</div>
                    <div class="last-message">${contact.lastMessage || ''}</div>
                    <div class="timestamp">${formatTime(contact.lastMessageTime)} | 有消息: ${contact.hasLastMessage ? '是' : '否'}</div>
                `;
                
                resultsDiv.appendChild(contactDiv);
            });
        }

        function testSorting() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>排序逻辑测试</h2>';
            
            const processedUsers = processUsers(mockApiResponse.users);
            
            resultsDiv.innerHTML += `
                <h3>排序结果说明：</h3>
                <ul>
                    <li>有最后消息的联系人排在前面（蓝色背景）</li>
                    <li>没有最后消息的联系人排在后面（灰色背景）</li>
                    <li>同类型内按时间倒序排列</li>
                    <li>lastMessage为null或空时，显示空白字符</li>
                </ul>
                <h3>实际排序结果：</h3>
            `;
            
            processedUsers.forEach((contact, index) => {
                const contactDiv = document.createElement('div');
                contactDiv.className = `contact-item ${contact.hasLastMessage ? 'has-message' : 'no-message'}`;
                
                contactDiv.innerHTML = `
                    <div class="contact-name">${index + 1}. ${contact.name}</div>
                    <div class="contact-status">${contact.status}</div>
                    <div class="last-message">最后消息: "${contact.lastMessage || '(空白)'}"</div>
                    <div class="timestamp">${formatTime(contact.lastMessageTime)} | 有消息: ${contact.hasLastMessage ? '是' : '否'}</div>
                `;
                
                resultsDiv.appendChild(contactDiv);
            });
        }
    </script>
</body>
</html>
