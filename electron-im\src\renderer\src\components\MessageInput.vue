<!-- 消息输入组件 -->
<template>
  <div class="border-t border-gray-200 bg-white p-4">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center gap-2">
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('insert-emoji')"
        >
          😊
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('attach-file')"
        >
          📎
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('insert-image')"
        >
          🖼️
        </button>
      </div>
      <button
        id="sendBtn"
        class="bg-blue-500 text-white border-none px-4 py-2 rounded-md cursor-pointer text-sm font-medium hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
        :disabled="!messageContent.trim()"
        @click="handleSend"
      >
        发送 (Enter)
      </button>
    </div>
    <div class="border border-gray-300 rounded-md bg-white">
      <textarea
        id="messageBox"
        ref="messageEditor"
        v-model="messageContent"
        class="w-full border-none outline-none p-3 text-sm font-inherit resize-y min-h-28 placeholder-gray-400"
        placeholder="输入消息... (Enter发送，Ctrl+Enter换行)"
        rows="6"
        @keydown="handleKeyDown"
      ></textarea>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  currentContact: Contact | null
}

defineProps<Props>()

const emit = defineEmits<{
  'send-message': [content: string]
  'insert-emoji': []
  'attach-file': []
  'insert-image': []
}>()

const messageContent = ref('')
const messageEditor = ref<HTMLTextAreaElement>()

const handleSend = () => {
  if (!messageContent.value.trim()) {
    return
  }

  // 发送消息 - 保持换行符，但移除首尾空白
  const content = messageContent.value.replace(/^\s+|\s+$/g, '')
  messageContent.value = ''

  // 触发发送事件
  emit('send-message', content)
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey) {
      // Ctrl+Enter: 插入换行符
      event.preventDefault()
      const textarea = event.target as HTMLTextAreaElement
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = textarea.value

      // 在光标位置插入换行符
      messageContent.value = value.substring(0, start) + '\n' + value.substring(end)

      // 设置光标位置到换行符后面
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 1
      }, 0)
    } else {
      // Enter: 发送消息
      event.preventDefault()
      handleSend()
    }
  }
}

// 暴露方法给父组件
defineExpose({
  focus: () => messageEditor.value?.focus(),
  clear: () => {
    messageContent.value = ''
  }
})
</script>
