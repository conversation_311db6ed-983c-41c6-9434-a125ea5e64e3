<!-- 登录页 -->
<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md">
      <div>
        <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">用户登录</h2>
      </div>

      <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700"> 用户名 </label>
            <input
              id="username"
              v-model="username"
              type="text"
              :class="[
                'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                usernameError ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="请输入用户名"
            />
            <p v-if="usernameError" class="mt-1 text-sm text-red-600">
              {{ usernameError }}
            </p>
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700"> 密码 </label>
            <input
              id="password"
              v-model="password"
              type="password"
              :class="[
                'mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                passwordError ? 'border-red-300' : 'border-gray-300'
              ]"
              placeholder="请输入密码"
            />
            <p v-if="passwordError" class="mt-1 text-sm text-red-600">
              {{ passwordError }}
            </p>
          </div>
        </div>

        <div v-if="errorMessage" class="bg-red-50 border border-red-200 rounded-md p-3">
          <p class="text-sm text-red-600">{{ errorMessage }}</p>
        </div>

        <div v-if="successMessage" class="bg-green-50 border border-green-200 rounded-md p-3">
          <p class="text-sm text-green-600">{{ successMessage }}</p>
        </div>

        <button
          type="submit"
          :disabled="isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </span>
          {{ isLoading ? '登录中...' : '登录' }}
        </button>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '../store/user'

// 用户状态管理
const userStore = useUserStore()

// 响应式数据
const username = ref('')
const password = ref('')
const usernameError = ref('')
const passwordError = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const isLoading = ref(false)

// 组件挂载时检查登录状态
onMounted(() => {
  userStore.initializeAuth()

  // 如果已经登录，用户状态会自动更新，App.vue会处理页面切换
  if (userStore.isAuthenticated.value) {
    console.log('用户已登录:', userStore.currentUser.value)
  }
})

// 表单校验
const validateForm = (): boolean => {
  usernameError.value = ''
  passwordError.value = ''

  if (!username.value.trim()) {
    usernameError.value = '用户名不能为空'
    return false
  }

  if (username.value.trim().length < 2) {
    usernameError.value = '用户名至少需要2个字符'
    return false
  }

  if (!password.value.trim()) {
    passwordError.value = '密码不能为空'
    return false
  }

  return true
}

// 处理登录
const handleLogin = async () => {
  // 清除之前的消息
  errorMessage.value = ''
  successMessage.value = ''

  // 表单校验
  if (!validateForm()) {
    return
  }

  isLoading.value = true

  try {
    console.log('开始登录流程')

    // 调用用户存储的登录方法
    const result = await userStore.login(username.value.trim(), password.value.trim())

    if (result.success) {
      successMessage.value = '登录成功！欢迎回来'
      console.log('登录成功:', userStore.currentUser)
      console.log('认证状态:', userStore.isAuthenticated)

      // 登录成功后，用户状态会自动更新，App.vue会自动切换到聊天页面
      setTimeout(() => {
        console.log('登录成功，即将跳转到聊天页面...')
        console.log('当前用户:', userStore.currentUser)
        console.log('认证状态:', userStore.isAuthenticated)
        // 用户状态已经更新，App.vue的计算属性会自动响应
      }, 1000)
    } else {
      errorMessage.value = result.message || '登录失败，请检查用户名和密码'
      console.log('登录失败')
    }
  } catch (error) {
    console.error('登录过程中发生错误:', error)

    if (error instanceof Error) {
      // 根据错误类型显示不同的错误信息
      if (error.message.includes('Bad Request') || error.message.includes('HTTP 400')) {
        errorMessage.value = '请求参数错误，请检查输入信息'
      } else if (error.message.includes('Unauthorized') || error.message.includes('HTTP 401')) {
        errorMessage.value = '用户名或密码错误'
      } else if (error.message.includes('Forbidden') || error.message.includes('HTTP 403')) {
        errorMessage.value = '账户已被锁定，请联系管理员'
      } else if (error.message.includes('Not Found') || error.message.includes('HTTP 404')) {
        errorMessage.value = '登录服务不可用，请联系管理员'
      } else if (
        error.message.includes('Too Many Requests') ||
        error.message.includes('HTTP 429')
      ) {
        errorMessage.value = '请求太频繁，请稍后再试'
      } else if (
        error.message.includes('Internal Server Error') ||
        error.message.includes('HTTP 500')
      ) {
        errorMessage.value = '服务器内部错误，请稍后重试'
      } else if (error.message.includes('请求超时')) {
        errorMessage.value = '网络请求超时，请重试'
      } else if (error.message.includes('网络')) {
        errorMessage.value = '网络连接失败，请检查网络设置'
      } else {
        errorMessage.value = error.message || '登录失败，请稍后重试'
      }
    } else {
      errorMessage.value = '登录失败，请稍后重试'
    }
  } finally {
    isLoading.value = false
  }
}
</script>
