<!-- 聊天侧边栏组件 -->
<template>
  <div class="w-80 h-screen bg-white border-r border-gray-200 flex flex-col">
    <!-- 搜索栏 -->
    <div class="p-4 border-b border-gray-200">
      <div class="relative">
        <img
          src="../assets/search-icon.svg"
          alt="搜索"
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-[18px] h-[18px]"
        />
        <input
          type="text"
          class="w-full pl-9 pr-10 py-2 bg-gray-100 border-none rounded-md outline-none text-sm"
          placeholder="搜索"
          v-model="searchQuery"
          @input="handleSearch"
        />
        <button
          class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-transparent border-none cursor-pointer p-1 rounded text-gray-500 hover:bg-gray-100"
          @click="$emit('refresh')"
          title="刷新联系人列表"
        >
          <img src="../assets/refresh-icon.svg" alt="刷新" class="w-5 h-5" />
        </button>
      </div>
    </div>

    <!-- 联系人列表 -->
    <div class="flex-1 overflow-y-auto">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex flex-col items-center justify-center p-8">
        <div class="text-gray-500 text-sm mb-2">{{ loadingText || '加载联系人中...' }}</div>
        <div
          class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></div>
      </div>

      <!-- 联系人列表 -->
      <div v-else>
        <ContactItem
          v-for="contact in filteredContacts"
          :key="contact.id"
          :contact="contact"
          :is-active="currentContactId === contact.id"
          @select="$emit('select-contact', $event)"
        />

        <!-- 空状态 -->
        <div
          v-if="!filteredContacts.length && !isLoading"
          class="flex items-center justify-center p-8"
        >
          <div class="text-gray-500 text-sm">暂无联系人</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ContactItem from './ContactItem.vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
  hasLastMessage?: boolean
}

interface Props {
  contacts: Contact[]
  currentContactId: string | null
  isLoading: boolean
  loadingText?: string
}

const props = defineProps<Props>()

defineEmits<{
  'select-contact': [id: string]
  refresh: []
}>()

// 搜索功能
const searchQuery = ref('')

const filteredContacts = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.contacts
  }
  return props.contacts.filter((contact) =>
    contact.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}
</script>
